variables:
  POETR<PERSON>_CACHE_DIR: $CI_PROJECT_DIR/.poetry-cache
  POETRY_VIRTUALENVS_IN_PROJECT: "true"

stages:
  - test
  - build
  - deploy
  - verify

.auto-main-auto-mr: &auto-main-auto-mr
  - if: $CI_MERGE_REQUEST_ID || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.auto-main-manual-mr: &auto-main-manual-mr
  - if: $CI_MERGE_REQUEST_ID
    when: manual
    allow_failure: true
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.manual-main: &manual-main
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    when: manual

.auto-main: &auto-main
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

backend unit tests:
  stage: test
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod
  script:
    - poetry install
    - source .venv/bin/activate
    - poe ci-test-unit
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure


backend integration tests:
  stage: test
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod
  script:
    - poetry install
    - source .venv/bin/activate
    - poe ci-test-integration
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure


backend lint:
  stage: test
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod
  script:
    - poetry install
    - source .venv/bin/activate
    - ruff format --check
    - ruff check --fix
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure


frontend tests:
  stage: test
  image: node:22.14.0
  variables:
      NPM_CONFIG_LOGLEVEL: silent
  script:
    - cd frontend
    - |
      source ../ci-cd/gitlab-sections.sh
      section "Installing dependencies" npm ci
      section "Running unit tests" npm run test:unit
      section "Linting" npm run ci:lint
      section "Linting stylelint" npm run ci:lint:css
      section "Checking formatting" npm run ci:format
      section "Checking types" npm run ci:type-check
  rules: *auto-main-auto-mr
  cache:
    key: frontend-deps
    paths:
      - frontend/node_modules/
  tags:
    - dpt-azure

build-image:
  stage: build
  dependencies: []
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - image-exists && echo "Image already exists, skipping build!" && exit 0
    - image-builder
  tags:
    - dpt-azure
  rules: *auto-main-auto-mr

.deploy:
  dependencies: []
  variables:
    GIT_STRATEGY: none
  stage:
    deploy
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - tag-image $ENV
  allow_failure: false
  tags:
    - dpt-azure

deploy-dev:
  extends: .deploy
  variables:
    ENV: dev
  rules: *auto-main-manual-mr

deploy-prod:
  extends: .deploy
  variables:
    ENV: prod
  rules: *manual-main


.post-deploy-check:
  dependencies: []
  stage: verify
  image: alpine:latest
  timeout: 5m
  script: |
    apk add --no-cache curl jq
    until response=$(curl -s -w "\n%{http_code}" $HEALTH_URL) && [ "$(echo "$response" | tail -n1)" != "502" ] && echo "$response" | sed '$d' | jq -e --arg COMMIT $CI_COMMIT_SHORT_SHA '.version == "commit-"+$COMMIT' >/dev/null; do
      [ "$(echo "$response" | tail -n1)" = "502" ] && echo "Server is not responding - 502 error" || echo "⏳ Waiting for deployment..."
      sleep 3
    done
    echo "✅ Deployment verified - found matching commit"
  tags:
    - dpt-azure

post-deploy-check-dev:
  extends: .post-deploy-check
  variables:
    HEALTH_URL: https://dataoffice.indiebi.dev/health
  needs:
    - deploy-dev
  rules: *auto-main-auto-mr

post-deploy-check-prod:
  extends: .post-deploy-check
  variables:
    HEALTH_URL: https://dataoffice.indiebi.com/health
  needs:
    - deploy-prod
  rules: *auto-main
