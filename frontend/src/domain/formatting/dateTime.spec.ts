import { describe, it, expect, vi, beforeEach } from 'vitest'
import { formatDateTimeISO, formatDateTimeISOWithSince, formatTimeSince } from './dateTime'
import { PlainDateTime } from '@/libs/temporal'
import * as temporal from '@/libs/temporal'

describe('dateTime formatting', () => {
  const now = new PlainDateTime(2025, 1, 1, 0, 0, 0)

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(temporal, 'getNow').mockReturnValue(now)
  })

  describe.each([
    [new PlainDateTime(2024, 12, 31, 23, 55, 0), '5m ago'],
    [new PlainDateTime(2024, 12, 31, 21, 30, 0), '2h ago'],
    [new PlainDateTime(2024, 12, 31, 22, 0, 0), '2h ago'],
    [new PlainDateTime(2024, 12, 30, 18, 30, 0), '1d ago'],
    [new PlainDateTime(2024, 12, 30, 0, 0, 0), '2d ago'],
    [new PlainDateTime(2025, 1, 1, 0, 0, 0), 'now'],
  ])('formatTimeSince(%s)', (date, expected) => {
    it(`returns ${expected}`, () => {
      expect(formatTimeSince(date)).toBe(expected)
    })
  })

  describe('formatDateTimeISOWithSince', () => {
    it('formatDateTimeISOWithSince returns null for null input', () => {
      expect(formatDateTimeISOWithSince(null)).toBe(null)
    })

    it('formatDateTimeISOWithSince formats timestamp with UTC suffix and relative time', () => {
      expect(formatDateTimeISOWithSince(new PlainDateTime(2024, 12, 31, 23, 55, 0))).toBe(
        '2024-12-31 23:55:00 UTC, 5m ago',
      )
    })
  })

  describe('formatDateTimeISO', () => {
    it('formatDateTimeISO returns null for null input', () => {
      expect(formatDateTimeISO(null)).toBe(null)
    })

    it('formatDateTimeISO formats PlainDateTime to ISO string with space instead of T', () => {
      expect(formatDateTimeISO(new PlainDateTime(2024, 1, 15, 10, 25, 30))).toBe(
        '2024-01-15 10:25:30',
      )
    })
  })
})
