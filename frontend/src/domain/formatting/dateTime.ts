import { getNow, type PlainDateTime } from '@/libs/temporal'

export function formatDateTimeISO(date: PlainDateTime | null): string | null {
  if (!date) return null
  return date.toString().replace('T', ' ')
}

export function formatTimeSince(date: PlainDateTime): string {
  const now = getNow()
  const duration = now.since(date).round({ smallestUnit: 'minute' })

  const units: [Intl.RelativeTimeFormatUnit, number][] = [
    ['day', duration.days],
    ['hour', duration.hours],
    ['minute', duration.minutes],
  ]

  const formatter = new Intl.RelativeTimeFormat('en-US', { style: 'narrow', numeric: 'always' })

  for (const [unit, value] of units) {
    if (value !== 0) {
      return formatter.format(-value, unit)
    }
  }

  return 'now'
}

export function formatDateTimeISOWithSince(date: PlainDateTime | null): string | null {
  if (!date) return null

  const formatted = formatDateTimeISO(date)
  const sinceText = formatTimeSince(date)

  return `${formatted} UTC, ${sinceText}`
}
