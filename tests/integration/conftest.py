import re
from typing import Literal

import dataoffice.settings
import pytest
from dataoffice.settings import DevSettings, SettingsEnv
from pydantic_settings import SettingsConfigDict

SCP_TEST_ACCOUNT_ORG_ID = "o-rsFPhw"
SCP_TEST_ACCOUNT_USER_ID = "u-mp09bS"


class TestSettings(DevSettings):
    env: Literal[SettingsEnv.test] = SettingsEnv.test  # type: ignore
    env_files: list[str] = [".env", ".env.dev", ".env.integration"]
    model_config = SettingsConfigDict(env_file=env_files, env_file_encoding="utf-8")

    auth_client_id: str = "test"
    auth_client_secret: str = "test"
    auth_tenant_id: str = "test"
    cookie_secret_key: str = "test"
    dataset_manager_key: str = "test"
    pipeline_manager_key: str = "test"

    # Keys below are currently used by integrations tests, and they are loaded from .env.dev file,
    # or from env variables when they are running on CI.
    report_service_key: str
    scraper_service_key: str
    user_service_key: str


# settings are imported first and immediately overwritten with dev settings
# so that modules can still use settings in module scope that executes on import
# Why TestSettings based on DevSettings? Because integrations tests are run against real dev environment,
# with assumption that data for test_user_id and test_organization_id will not change.
# ruff: noqa: E402
dataoffice.settings._settings = TestSettings()  # type: ignore


@pytest.fixture
def test_organization_id():
    return SCP_TEST_ACCOUNT_ORG_ID


@pytest.fixture
def test_user_id():
    return SCP_TEST_ACCOUNT_USER_ID


class RegexMatcher:
    def __init__(self, regex):
        self.regex = regex

    def __eq__(self, other):
        return bool(re.match(self.regex, other))

    def __repr__(self):
        return f"RegexMatcher({self.regex!r})"


class TypeMatcher:
    def __init__(self, expected_type):
        self.expected_type = expected_type

    def __eq__(self, other):
        return isinstance(other, self.expected_type)

    def __repr__(self):
        return f"TypeMatcher({self.expected_type.__name__})"


class AnyOfMatcher:
    def __init__(self, values):
        self.values = values

    def __eq__(self, other):
        return other in self.values

    def __repr__(self):
        return f"AnyOfMatcher({self.values})"


class EnumMatcher:
    def __init__(self, enum_class):
        self.enum_class = enum_class

    def __eq__(self, other):
        if isinstance(other, self.enum_class):
            return True
        try:
            self.enum_class(other)
            return True
        except (ValueError, KeyError):
            return False

    def __repr__(self):
        values = [item.value for item in self.enum_class]
        return f"EnumMatcher({self.enum_class.__name__}: {values})"


class ListMatcher:
    def __init__(self, item_pattern):
        self.item_pattern = item_pattern

    def __eq__(self, other):
        if not isinstance(other, list):
            return False
        return all(item == self.item_pattern for item in other)

    def __repr__(self):
        return f"ListMatcher({self.item_pattern})"


class MatcherFactory:
    @property
    def anything(self):
        return TypeMatcher(object)

    def any(self, expected_type):
        return TypeMatcher(expected_type)

    def list_of(self, pattern):
        return ListMatcher(pattern)

    def regex(self, pattern):
        """Matcher that checks regex pattern"""
        return RegexMatcher(pattern)

    def any_of(self, *values):
        """Matcher that checks if value is one of the provided values"""
        return AnyOfMatcher(values)

    @property
    def short_iso_date_string(self):
        """Matcher for dates in YYYY-MM-DD format"""
        return RegexMatcher(r"\d{4}-\d{2}-\d{2}")

    @property
    def uuid(self):
        """Matcher for UUIDs with format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"""
        group = r"[0-9a-f]"
        return RegexMatcher(
            f"^{group}{{8}}-{group}{{4}}-{group}{{4}}-{group}{{4}}-{group}{{12}}$"
        )

    def str_enum(self, enum_class):
        return EnumMatcher(enum_class)


@pytest.fixture
def match():
    return MatcherFactory()
