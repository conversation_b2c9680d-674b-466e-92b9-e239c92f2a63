import pytest
from dataoffice.connectors.report_service import get_organization_coverage


@pytest.mark.asyncio(scope="session")
async def test_get_organization_coverage_for_scp(test_organization_id, match):
    coverage = await get_organization_coverage(test_organization_id)

    assert coverage == {
        "playstation_sales": [
            {
                "date_from": match.short_iso_date_string,
                "date_to": match.short_iso_date_string,
            },
        ],
        "playstation_wishlist_actions": [
            {
                "date_from": match.short_iso_date_string,
                "date_to": match.short_iso_date_string,
            },
        ],
    }
