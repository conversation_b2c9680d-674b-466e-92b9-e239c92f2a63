import datetime

import pytest
from dataoffice.connectors.scrapers_service import (
    ScraperBinaryStatus,
    get_scraper_operation_history,
    get_scraper_statuses,
)

fromISO = datetime.datetime.fromisoformat


@pytest.mark.asyncio(scope="session")
async def test_get_scraper_statuses(test_organization_id, test_user_id):
    statuses = await get_scraper_statuses()
    scp_test_account_statuses = [
        s.model_dump() for s in statuses if s.organization_id == test_organization_id
    ]

    # checking only playstation sources for simplicity of the test
    scp_test_account_statuses = [
        s for s in scp_test_account_statuses if s["source"].startswith("playstation")
    ]

    assert len(scp_test_account_statuses) == 2

    assert scp_test_account_statuses == [
        {
            "consecutive_failed_scrape_count": 0,
            "created_at": fromISO("2025-04-18T11:28:21.399328+00:00"),
            "last_fail_reason": None,
            "last_fail_timestamp": None,
            "last_operation_id": "0b20b3e8-a8da-401b-8985-d7c42083ae1d",
            "last_origin": "ScraperLib_1.68.0",
            "last_success_timestamp": fromISO("2025-08-06T17:06:29.866083+00:00"),
            "last_report_ids": [175876],
            "last_scrape_date_ranges": [
                {"date_from": "2025-08-03", "date_to": "2025-08-06", "days_in_range": 4}
            ],
            "user_id": test_user_id,
            "organization_id": test_organization_id,
            "source": "playstation_wishlist_actions",
            "state": ScraperBinaryStatus.FINISHED,
            "updated_at": fromISO("2025-08-06T17:06:29.866083+00:00"),
            "version": 0,
        },
        {
            "consecutive_failed_scrape_count": 0,
            "created_at": fromISO("2025-04-18T11:28:21.399328+00:00"),
            "last_fail_reason": None,
            "last_fail_timestamp": None,
            "last_operation_id": "d5a346ca-db3c-41b5-9e33-8754d1823d0a",
            "last_origin": "ScraperLib_1.68.0",
            "last_success_timestamp": fromISO("2025-08-06T17:05:09.834711+00:00"),
            "last_report_ids": [175875],
            "last_scrape_date_ranges": [
                {"date_from": "2025-08-03", "date_to": "2025-08-06", "days_in_range": 4}
            ],
            "user_id": test_user_id,
            "organization_id": test_organization_id,
            "source": "playstation_sales",
            "state": ScraperBinaryStatus.FINISHED,
            "updated_at": fromISO("2025-08-06T17:05:09.834711+00:00"),
            "version": 0,
        },
    ]


@pytest.mark.asyncio(scope="session")
async def test_get_scraper_operation_history(test_organization_id, match):
    history = await get_scraper_operation_history(
        organization_id=test_organization_id,
    )

    history_dumps = [h.model_dump() for h in history]

    assert history_dumps == match.list_of(
        {
            "organization_id": test_organization_id,
            "source": match.any(str),
            "operation_id": match.uuid,
            "state": match.str_enum(ScraperBinaryStatus),
            "created_at": match.any(datetime.datetime),
            "updated_at": match.any(datetime.datetime),
            "start_timestamp": match.any(datetime.datetime),
            "end_timestamp": match.any_of(None, match.any(datetime.datetime)),
            "fail_reason": match.anything,
            "execution_time": match.anything,
        }
    )
